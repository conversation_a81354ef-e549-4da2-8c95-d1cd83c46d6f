.download-manager {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.download-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.download-manager-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #212529;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.download-manager-filters {
  display: flex;
  gap: 12px;
  padding: 16px 20px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
}

.search-input,
.filter-select {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.search-input {
  flex: 1;
}

.search-input:focus,
.filter-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.downloads-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #6c757d;
  font-size: 16px;
}

.download-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}

.download-item:hover {
  background: #f8f9fa;
}

.download-info {
  flex: 1;
  min-width: 0;
}

.download-filename {
  font-weight: 500;
  color: #212529;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.download-url {
  font-size: 12px;
  color: #6c757d;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 8px;
}

.download-progress {
  margin-top: 8px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-fill {
  height: 100%;
  background: #007bff;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #6c757d;
}

.download-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin: 0 16px;
  min-width: 120px;
}

.download-status {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.download-speed {
  font-size: 12px;
  color: #28a745;
  font-weight: 500;
}

.download-error {
  font-size: 12px;
  color: #dc3545;
  max-width: 120px;
  text-align: right;
  word-wrap: break-word;
}

.download-actions {
  display: flex;
  gap: 8px;
}

.download-actions button {
  padding: 6px 8px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 4px;
  font-size: 16px;
  transition: background-color 0.2s ease;
}

.download-actions button:hover {
  background: #e9ecef;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.modal-header button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #6c757d;
}

.modal-header button:hover {
  background: #e9ecef;
}

.modal-body {
  padding: 20px;
}

.url-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.url-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.modal-footer button {
  padding: 8px 16px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-footer button:first-child {
  background: white;
  color: #6c757d;
}

.modal-footer button:first-child:hover {
  background: #f8f9fa;
}

.modal-footer button:last-child {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.modal-footer button:last-child:hover:not(:disabled) {
  background: #0056b3;
  border-color: #0056b3;
}

.modal-footer button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Темная тема */
@media (prefers-color-scheme: dark) {
  .download-manager {
    background: #1a1a1a;
    color: #ffffff;
  }

  .download-manager-header {
    background: #2d2d2d;
    border-bottom-color: #404040;
  }

  .download-manager-header h2 {
    color: #ffffff;
  }

  .download-manager-filters {
    background: #1a1a1a;
    border-bottom-color: #404040;
  }

  .search-input,
  .filter-select {
    background: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
  }

  .download-item {
    border-bottom-color: #404040;
  }

  .download-item:hover {
    background: #2d2d2d;
  }

  .download-filename {
    color: #ffffff;
  }

  .progress-bar {
    background: #404040;
  }

  .modal {
    background: #1a1a1a;
    color: #ffffff;
  }

  .modal-header,
  .modal-footer {
    background: #2d2d2d;
    border-color: #404040;
  }

  .url-input {
    background: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
  }
}
