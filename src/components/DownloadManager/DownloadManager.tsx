import React, { useState, useEffect, useCallback } from 'react';
import { IntelligentDownloadManager, DownloadItem, DownloadFilters } from '../../../core/download-manager/advanced-download-system';
import './DownloadManager.module.css';

interface DownloadManagerProps {
  className?: string;
}

export const DownloadManager: React.FC<DownloadManagerProps> = ({ className }) => {
  const [downloadManager] = useState(() => new IntelligentDownloadManager());
  const [downloads, setDownloads] = useState<DownloadItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<DownloadFilters>({});
  const [selectedDownloads, setSelectedDownloads] = useState<Set<string>>(new Set());
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newDownloadUrl, setNewDownloadUrl] = useState('');

  // Обновление списка загрузок
  const updateDownloads = useCallback(() => {
    const allDownloads = downloadManager.getAllDownloads();
    const filteredDownloads = downloadManager.searchDownloads(searchQuery, filters);
    setDownloads(filteredDownloads);
  }, [downloadManager, searchQuery, filters]);

  // Подписка на события прогресса
  useEffect(() => {
    const handleProgress = (event: CustomEvent) => {
      updateDownloads();
    };

    window.addEventListener('download-progress', handleProgress as EventListener);
    
    // Обновление каждые 500мс
    const interval = setInterval(updateDownloads, 500);

    return () => {
      window.removeEventListener('download-progress', handleProgress as EventListener);
      clearInterval(interval);
    };
  }, [updateDownloads]);

  // Инициализация
  useEffect(() => {
    updateDownloads();
  }, [updateDownloads]);

  // Добавление новой загрузки
  const handleAddDownload = async () => {
    if (newDownloadUrl.trim()) {
      try {
        await downloadManager.addDownload(newDownloadUrl.trim(), {
          priority: 'normal',
          category: 'general'
        });
        setNewDownloadUrl('');
        setShowAddDialog(false);
        updateDownloads();
      } catch (error) {
        console.error('Failed to add download:', error);
      }
    }
  };

  // Управление загрузками
  const handlePause = async (downloadId: string) => {
    await downloadManager.pauseDownload(downloadId);
    updateDownloads();
  };

  const handleResume = async (downloadId: string) => {
    await downloadManager.resumeDownload(downloadId);
    updateDownloads();
  };

  const handleCancel = async (downloadId: string) => {
    await downloadManager.cancelDownload(downloadId);
    updateDownloads();
  };

  // Массовые операции
  const handleBulkAction = async (action: 'pause' | 'resume' | 'cancel') => {
    const promises = Array.from(selectedDownloads).map(downloadId => {
      switch (action) {
        case 'pause':
          return downloadManager.pauseDownload(downloadId);
        case 'resume':
          return downloadManager.resumeDownload(downloadId);
        case 'cancel':
          return downloadManager.cancelDownload(downloadId);
      }
    });

    await Promise.all(promises);
    setSelectedDownloads(new Set());
    updateDownloads();
  };

  // Форматирование размера файла
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Форматирование скорости
  const formatSpeed = (bytesPerSecond: number): string => {
    return formatFileSize(bytesPerSecond) + '/s';
  };

  // Форматирование времени
  const formatTime = (seconds: number): string => {
    if (!isFinite(seconds)) return '∞';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Получение иконки статуса
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'downloading':
        return '⬇️';
      case 'paused':
        return '⏸️';
      case 'completed':
        return '✅';
      case 'failed':
        return '❌';
      case 'scheduled':
        return '⏰';
      default:
        return '⏳';
    }
  };

  // Получение цвета статуса
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'downloading':
        return '#007bff';
      case 'paused':
        return '#ffc107';
      case 'completed':
        return '#28a745';
      case 'failed':
        return '#dc3545';
      case 'scheduled':
        return '#6f42c1';
      default:
        return '#6c757d';
    }
  };

  return (
    <div className={`download-manager ${className || ''}`}>
      {/* Заголовок и панель управления */}
      <div className="download-manager-header">
        <h2>Менеджер загрузок</h2>
        <div className="download-manager-controls">
          <button 
            className="btn btn-primary"
            onClick={() => setShowAddDialog(true)}
          >
            + Добавить загрузку
          </button>
          
          {selectedDownloads.size > 0 && (
            <div className="bulk-actions">
              <button onClick={() => handleBulkAction('pause')}>⏸️ Пауза</button>
              <button onClick={() => handleBulkAction('resume')}>▶️ Продолжить</button>
              <button onClick={() => handleBulkAction('cancel')}>❌ Отменить</button>
            </div>
          )}
        </div>
      </div>

      {/* Поиск и фильтры */}
      <div className="download-manager-filters">
        <input
          type="text"
          placeholder="Поиск загрузок..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="search-input"
        />
        
        <select
          value={filters.status || ''}
          onChange={(e) => setFilters({ ...filters, status: e.target.value || undefined })}
          className="filter-select"
        >
          <option value="">Все статусы</option>
          <option value="downloading">Загружается</option>
          <option value="paused">Приостановлено</option>
          <option value="completed">Завершено</option>
          <option value="failed">Ошибка</option>
          <option value="scheduled">Запланировано</option>
        </select>

        <select
          value={filters.priority || ''}
          onChange={(e) => setFilters({ ...filters, priority: e.target.value || undefined })}
          className="filter-select"
        >
          <option value="">Все приоритеты</option>
          <option value="urgent">Срочно</option>
          <option value="high">Высокий</option>
          <option value="normal">Обычный</option>
          <option value="low">Низкий</option>
        </select>
      </div>

      {/* Список загрузок */}
      <div className="downloads-list">
        {downloads.length === 0 ? (
          <div className="empty-state">
            <p>Нет загрузок</p>
          </div>
        ) : (
          downloads.map((download) => (
            <div key={download.id} className="download-item">
              <div className="download-checkbox">
                <input
                  type="checkbox"
                  checked={selectedDownloads.has(download.id)}
                  onChange={(e) => {
                    const newSelected = new Set(selectedDownloads);
                    if (e.target.checked) {
                      newSelected.add(download.id);
                    } else {
                      newSelected.delete(download.id);
                    }
                    setSelectedDownloads(newSelected);
                  }}
                />
              </div>

              <div className="download-info">
                <div className="download-filename">{download.filename}</div>
                <div className="download-url">{download.url}</div>
                
                {download.status === 'downloading' && (
                  <div className="download-progress">
                    <div className="progress-bar">
                      <div 
                        className="progress-fill"
                        style={{ 
                          width: `${download.size > 0 ? (download.downloadedSize / download.size) * 100 : 0}%` 
                        }}
                      />
                    </div>
                    <div className="progress-text">
                      {formatFileSize(download.downloadedSize)} / {formatFileSize(download.size)} 
                      ({download.size > 0 ? Math.round((download.downloadedSize / download.size) * 100) : 0}%)
                    </div>
                  </div>
                )}
              </div>

              <div className="download-stats">
                <div className="download-status" style={{ color: getStatusColor(download.status) }}>
                  {getStatusIcon(download.status)} {download.status}
                </div>
                
                {download.status === 'downloading' && (
                  <>
                    <div className="download-speed">{formatSpeed(download.speed)}</div>
                    <div className="download-eta">
                      ETA: {formatTime(download.speed > 0 ? (download.size - download.downloadedSize) / download.speed : 0)}
                    </div>
                  </>
                )}

                {download.error && (
                  <div className="download-error">{download.error}</div>
                )}
              </div>

              <div className="download-actions">
                {download.status === 'downloading' && (
                  <button onClick={() => handlePause(download.id)} title="Пауза">⏸️</button>
                )}
                
                {download.status === 'paused' && (
                  <button onClick={() => handleResume(download.id)} title="Продолжить">▶️</button>
                )}
                
                {(download.status === 'downloading' || download.status === 'paused' || download.status === 'pending') && (
                  <button onClick={() => handleCancel(download.id)} title="Отменить">❌</button>
                )}

                {download.status === 'failed' && download.retryCount < download.maxRetries && (
                  <button onClick={() => handleResume(download.id)} title="Повторить">🔄</button>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Диалог добавления загрузки */}
      {showAddDialog && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>Добавить загрузку</h3>
              <button onClick={() => setShowAddDialog(false)}>✕</button>
            </div>
            
            <div className="modal-body">
              <input
                type="url"
                placeholder="Введите URL для загрузки..."
                value={newDownloadUrl}
                onChange={(e) => setNewDownloadUrl(e.target.value)}
                className="url-input"
                autoFocus
              />
            </div>
            
            <div className="modal-footer">
              <button onClick={() => setShowAddDialog(false)}>Отмена</button>
              <button onClick={handleAddDownload} disabled={!newDownloadUrl.trim()}>
                Добавить
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
