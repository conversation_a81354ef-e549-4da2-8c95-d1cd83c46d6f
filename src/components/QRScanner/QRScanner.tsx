import React, { useState, useEffect, useRef } from 'react';
import { IntelligentQRScanner, QRCode, ScanResult } from '../../../core/qr-scanner/advanced-qr-system';
import './QRScanner.module.css';

interface QRScannerProps {
  className?: string;
  onQRScanned?: (qrCode: QRCode) => void;
}

export const QRScanner: React.FC<QRScannerProps> = ({ 
  className, 
  onQRScanned 
}) => {
  const [qrScanner] = useState(() => new IntelligentQRScanner());
  const [qrHistory, setQRHistory] = useState<QRCode[]>([]);
  const [selectedQR, setSelectedQR] = useState<QRCode | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [isCameraActive, setIsCameraActive] = useState(false);
  const [scanMode, setScanMode] = useState<'camera' | 'file' | 'generate'>('camera');
  const [availableCameras, setAvailableCameras] = useState<MediaDeviceInfo[]>([]);
  const [selectedCamera, setSelectedCamera] = useState<string>('');
  const [lastScanResult, setLastScanResult] = useState<ScanResult | null>(null);

  // Генератор QR-кодов
  const [generateContent, setGenerateContent] = useState('');
  const [generateFormat, setGenerateFormat] = useState<'text' | 'url' | 'email' | 'phone'>('text');
  const [generatedQR, setGeneratedQR] = useState<string>('');

  const videoRef = useRef<HTMLVideoElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Загрузка истории QR-кодов
  useEffect(() => {
    loadQRHistory();
    loadAvailableCameras();
  }, []);

  const loadQRHistory = async () => {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith('qr_'));
      const history: QRCode[] = [];
      
      for (const key of keys) {
        const data = localStorage.getItem(key);
        if (data) {
          try {
            const qrCode = JSON.parse(data);
            history.push(qrCode);
          } catch (error) {
            console.error('Failed to parse QR code:', error);
          }
        }
      }
      
      setQRHistory(history.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      ));
    } catch (error) {
      console.error('Failed to load QR history:', error);
    }
  };

  const loadAvailableCameras = async () => {
    try {
      const cameras = await qrScanner.getAvailableCameras();
      setAvailableCameras(cameras);
      if (cameras.length > 0 && !selectedCamera) {
        setSelectedCamera(cameras[0].deviceId);
      }
    } catch (error) {
      console.error('Failed to load cameras:', error);
    }
  };

  // Запуск камеры
  const startCamera = async () => {
    if (!videoRef.current) return;

    try {
      const success = selectedCamera 
        ? await qrScanner.switchCamera(videoRef.current, selectedCamera)
        : await qrScanner.startCamera(videoRef.current);

      if (success) {
        setIsCameraActive(true);
        startScanning();
      } else {
        alert('Не удалось запустить камеру. Проверьте разрешения.');
      }
    } catch (error) {
      console.error('Failed to start camera:', error);
      alert('Ошибка при запуске камеры');
    }
  };

  // Остановка камеры
  const stopCamera = () => {
    qrScanner.stopCamera();
    setIsCameraActive(false);
    setIsScanning(false);
  };

  // Начало сканирования
  const startScanning = () => {
    if (!videoRef.current || !isCameraActive) return;

    setIsScanning(true);
    qrScanner.startContinuousScanning(videoRef.current, (result: ScanResult) => {
      setLastScanResult(result);
      
      if (result.success && result.qrCode) {
        setQRHistory(prev => [result.qrCode!, ...prev]);
        setSelectedQR(result.qrCode);
        onQRScanned?.(result.qrCode);
        
        // Остановить сканирование после успешного обнаружения
        stopScanning();
      }
    });
  };

  // Остановка сканирования
  const stopScanning = () => {
    qrScanner.stopContinuousScanning();
    setIsScanning(false);
  };

  // Сканирование из файла
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const result = await qrScanner.scanFromFile(file);
      setLastScanResult(result);
      
      if (result.success && result.qrCode) {
        setQRHistory(prev => [result.qrCode!, ...prev]);
        setSelectedQR(result.qrCode);
        onQRScanned?.(result.qrCode);
      } else {
        alert(result.error || 'QR-код не найден в изображении');
      }
    } catch (error) {
      console.error('Failed to scan file:', error);
      alert('Ошибка при сканировании файла');
    }
  };

  // Генерация QR-кода
  const generateQRCode = async () => {
    if (!generateContent.trim()) return;

    try {
      let content = generateContent.trim();
      
      // Форматирование контента в зависимости от типа
      switch (generateFormat) {
        case 'url':
          if (!content.startsWith('http://') && !content.startsWith('https://')) {
            content = 'https://' + content;
          }
          break;
        case 'email':
          if (!content.startsWith('mailto:')) {
            content = 'mailto:' + content;
          }
          break;
        case 'phone':
          if (!content.startsWith('tel:')) {
            content = 'tel:' + content;
          }
          break;
      }

      // Заглушка для генерации QR-кода
      const qrDataUrl = await generateQRCodeImage(content);
      setGeneratedQR(qrDataUrl);

      // Создать объект QR-кода и добавить в историю
      const qrCode: QRCode = {
        id: 'generated_' + Date.now(),
        content,
        type: {
          name: generateFormat,
          description: `Generated ${generateFormat}`,
          pattern: /.*/,
          parser: (c) => ({ content: c }),
          generator: (d) => d.content
        },
        format: generateFormat,
        timestamp: new Date(),
        source: 'generated',
        metadata: {
          size: { width: 200, height: 200 },
          errorCorrectionLevel: 'M',
          version: 1,
          maskPattern: 0,
          dataCapacity: 100,
          actualDataLength: content.length,
          redundancy: 0.3
        },
        actions: []
      };

      setQRHistory(prev => [qrCode, ...prev]);
      setSelectedQR(qrCode);
      
      // Сохранить в localStorage
      localStorage.setItem(`qr_${qrCode.id}`, JSON.stringify(qrCode));
      
    } catch (error) {
      console.error('Failed to generate QR code:', error);
      alert('Ошибка при генерации QR-кода');
    }
  };

  // Выполнение действия с QR-кодом
  const executeAction = async (qrCode: QRCode, actionType: string) => {
    try {
      switch (actionType) {
        case 'copy':
          await navigator.clipboard.writeText(qrCode.content);
          alert('Текст скопирован в буфер обмена');
          break;
        case 'open':
          if (qrCode.format === 'url') {
            window.open(qrCode.content, '_blank');
          } else if (qrCode.format === 'email') {
            window.open(qrCode.content);
          } else if (qrCode.format === 'phone') {
            window.open(qrCode.content);
          }
          break;
        case 'delete':
          if (confirm('Удалить этот QR-код из истории?')) {
            localStorage.removeItem(`qr_${qrCode.id}`);
            setQRHistory(prev => prev.filter(qr => qr.id !== qrCode.id));
            if (selectedQR?.id === qrCode.id) {
              setSelectedQR(null);
            }
          }
          break;
      }
    } catch (error) {
      console.error('Failed to execute action:', error);
      alert('Ошибка при выполнении действия');
    }
  };

  // Получение иконки для типа QR-кода
  const getQRIcon = (format: string) => {
    switch (format) {
      case 'url': return '🔗';
      case 'email': return '📧';
      case 'phone': return '📞';
      case 'text': return '📝';
      case 'wifi': return '📶';
      case 'location': return '📍';
      default: return '📄';
    }
  };

  // Получение placeholder для формата
  const getPlaceholderForFormat = (format: string) => {
    switch (format) {
      case 'url': return 'https://example.com';
      case 'email': return '<EMAIL>';
      case 'phone': return '+1234567890';
      case 'text': return 'Введите текст...';
      default: return 'Введите содержимое...';
    }
  };

  // Заглушка для генерации QR-кода
  const generateQRCodeImage = async (content: string): Promise<string> => {
    // В реальности здесь должна быть библиотека для генерации QR-кодов
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = 200;
    canvas.height = 200;
    
    if (ctx) {
      // Простая заглушка - белый квадрат с текстом
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, 200, 200);
      ctx.fillStyle = '#000000';
      ctx.strokeRect(10, 10, 180, 180);
      ctx.font = '12px Arial';
      ctx.fillText('QR Code', 80, 100);
      ctx.fillText(content.substring(0, 20), 20, 120);
    }
    
    return canvas.toDataURL();
  };

  // Скачивание QR-кода
  const downloadQRCode = (dataUrl: string) => {
    const a = document.createElement('a');
    a.href = dataUrl;
    a.download = `qr-code-${Date.now()}.png`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  return (
    <div className={`qr-scanner ${className || ''}`}>
      {/* Панель управления */}
      <div className="scanner-controls">
        <div className="mode-selector">
          <button
            className={scanMode === 'camera' ? 'active' : ''}
            onClick={() => setScanMode('camera')}
          >
            📷 Камера
          </button>
          <button
            className={scanMode === 'file' ? 'active' : ''}
            onClick={() => setScanMode('file')}
          >
            📁 Файл
          </button>
          <button
            className={scanMode === 'generate' ? 'active' : ''}
            onClick={() => setScanMode('generate')}
          >
            ⚡ Создать
          </button>
        </div>
      </div>

      <div className="scanner-content">
        {/* Область сканирования/генерации */}
        <div className="scanner-main">
          {scanMode === 'camera' && (
            <div className="camera-scanner">
              <div className="camera-controls">
                {availableCameras.length > 1 && (
                  <select
                    value={selectedCamera}
                    onChange={(e) => setSelectedCamera(e.target.value)}
                    disabled={isCameraActive}
                  >
                    {availableCameras.map((camera) => (
                      <option key={camera.deviceId} value={camera.deviceId}>
                        {camera.label || `Camera ${camera.deviceId.slice(0, 8)}`}
                      </option>
                    ))}
                  </select>
                )}
                
                <div className="camera-buttons">
                  {!isCameraActive ? (
                    <button onClick={startCamera} className="start-camera-btn">
                      📷 Запустить камеру
                    </button>
                  ) : (
                    <>
                      <button onClick={stopCamera} className="stop-camera-btn">
                        ⏹️ Остановить
                      </button>
                      {!isScanning ? (
                        <button onClick={startScanning} className="start-scan-btn">
                          🔍 Сканировать
                        </button>
                      ) : (
                        <button onClick={stopScanning} className="stop-scan-btn">
                          ⏸️ Остановить сканирование
                        </button>
                      )}
                    </>
                  )}
                </div>
              </div>

              <div className="video-container">
                <video
                  ref={videoRef}
                  className="scanner-video"
                  playsInline
                  muted
                />
                {isScanning && (
                  <div className="scanning-overlay">
                    <div className="scan-frame"></div>
                    <div className="scan-status">Сканирование...</div>
                  </div>
                )}
              </div>

              {lastScanResult && (
                <div className={`scan-result ${lastScanResult.success ? 'success' : 'error'}`}>
                  {lastScanResult.success ? (
                    <span>✅ QR-код найден!</span>
                  ) : (
                    <span>❌ {lastScanResult.error}</span>
                  )}
                  <span className="processing-time">
                    ({lastScanResult.processingTime.toFixed(0)}мс)
                  </span>
                </div>
              )}
            </div>
          )}

          {scanMode === 'file' && (
            <div className="file-scanner">
              <div className="file-upload-area">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  style={{ display: 'none' }}
                />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="file-upload-btn"
                >
                  📁 Выберите изображение с QR-кодом
                </button>
                <p className="file-hint">
                  Поддерживаются форматы: PNG, JPG, GIF, WebP
                </p>
              </div>
            </div>
          )}

          {scanMode === 'generate' && (
            <div className="qr-generator">
              <div className="generator-form">
                <div className="form-group">
                  <label>Тип содержимого:</label>
                  <select
                    value={generateFormat}
                    onChange={(e) => setGenerateFormat(e.target.value as any)}
                  >
                    <option value="text">Текст</option>
                    <option value="url">URL</option>
                    <option value="email">Email</option>
                    <option value="phone">Телефон</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>Содержимое:</label>
                  <textarea
                    value={generateContent}
                    onChange={(e) => setGenerateContent(e.target.value)}
                    placeholder={getPlaceholderForFormat(generateFormat)}
                    rows={3}
                  />
                </div>

                <button
                  onClick={generateQRCode}
                  disabled={!generateContent.trim()}
                  className="generate-btn"
                >
                  ⚡ Создать QR-код
                </button>
              </div>

              {generatedQR && (
                <div className="generated-qr">
                  <img src={generatedQR} alt="Generated QR Code" />
                  <div className="qr-actions">
                    <button onClick={() => downloadQRCode(generatedQR)}>
                      ⬇️ Скачать
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* История QR-кодов */}
        <div className="qr-history">
          <h3>История QR-кодов ({qrHistory.length})</h3>
          
          {qrHistory.length === 0 ? (
            <div className="empty-history">
              <p>История пуста</p>
            </div>
          ) : (
            <div className="history-list">
              {qrHistory.map((qrCode) => (
                <div
                  key={qrCode.id}
                  className={`history-item ${selectedQR?.id === qrCode.id ? 'selected' : ''}`}
                  onClick={() => setSelectedQR(qrCode)}
                >
                  <div className="qr-icon">{getQRIcon(qrCode.format)}</div>
                  
                  <div className="qr-info">
                    <div className="qr-content">
                      {qrCode.content.length > 50 
                        ? qrCode.content.substring(0, 50) + '...' 
                        : qrCode.content
                      }
                    </div>
                    <div className="qr-meta">
                      {qrCode.format} • {qrCode.source} • {new Date(qrCode.timestamp).toLocaleString()}
                    </div>
                  </div>

                  <div className="qr-actions">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        executeAction(qrCode, 'copy');
                      }}
                      title="Копировать"
                    >
                      📋
                    </button>
                    
                    {(qrCode.format === 'url' || qrCode.format === 'email' || qrCode.format === 'phone') && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          executeAction(qrCode, 'open');
                        }}
                        title="Открыть"
                      >
                        🔗
                      </button>
                    )}
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        executeAction(qrCode, 'delete');
                      }}
                      title="Удалить"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Детали выбранного QR-кода */}
      {selectedQR && (
        <div className="qr-details">
          <h3>Детали QR-кода</h3>
          
          <div className="detail-item">
            <span>Содержимое:</span>
            <span>{selectedQR.content}</span>
          </div>
          
          <div className="detail-item">
            <span>Тип:</span>
            <span>{selectedQR.format}</span>
          </div>
          
          <div className="detail-item">
            <span>Источник:</span>
            <span>{selectedQR.source === 'scanned' ? 'Отсканирован' : 'Создан'}</span>
          </div>
          
          <div className="detail-item">
            <span>Время:</span>
            <span>{new Date(selectedQR.timestamp).toLocaleString()}</span>
          </div>
          
          <div className="detail-item">
            <span>Размер данных:</span>
            <span>{selectedQR.metadata.actualDataLength} байт</span>
          </div>
        </div>
      )}
    </div>
  );
};
