.qr-scanner {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.scanner-controls {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.mode-selector {
  display: flex;
  gap: 8px;
}

.mode-selector button {
  padding: 8px 16px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.mode-selector button:hover {
  background: #f8f9fa;
}

.mode-selector button.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.scanner-content {
  display: flex;
  flex: 1;
  min-height: 0;
}

.scanner-main {
  flex: 2;
  display: flex;
  flex-direction: column;
  background: #ffffff;
}

.camera-scanner {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.camera-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.camera-controls select {
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

.camera-buttons {
  display: flex;
  gap: 8px;
}

.camera-buttons button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.start-camera-btn {
  background: #28a745;
  color: white;
}

.start-camera-btn:hover {
  background: #218838;
}

.stop-camera-btn {
  background: #dc3545;
  color: white;
}

.stop-camera-btn:hover {
  background: #c82333;
}

.start-scan-btn {
  background: #007bff;
  color: white;
}

.start-scan-btn:hover {
  background: #0056b3;
}

.stop-scan-btn {
  background: #ffc107;
  color: #212529;
}

.stop-scan-btn:hover {
  background: #e0a800;
}

.video-container {
  flex: 1;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000000;
}

.scanner-video {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
}

.scanning-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  pointer-events: none;
}

.scan-frame {
  width: 200px;
  height: 200px;
  border: 2px solid #007bff;
  border-radius: 8px;
  position: relative;
  animation: pulse 2s infinite;
}

.scan-frame::before,
.scan-frame::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid #007bff;
}

.scan-frame::before {
  top: -3px;
  left: -3px;
  border-right: none;
  border-bottom: none;
}

.scan-frame::after {
  bottom: -3px;
  right: -3px;
  border-left: none;
  border-top: none;
}

.scan-status {
  margin-top: 20px;
  color: white;
  font-size: 16px;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.scan-result {
  padding: 12px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

.scan-result.success {
  background: #d4edda;
  color: #155724;
  border-top: 1px solid #c3e6cb;
}

.scan-result.error {
  background: #f8d7da;
  color: #721c24;
  border-top: 1px solid #f5c6cb;
}

.processing-time {
  font-size: 12px;
  opacity: 0.7;
}

.file-scanner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.file-upload-area {
  text-align: center;
  padding: 40px;
}

.file-upload-btn {
  padding: 16px 32px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.file-upload-btn:hover {
  background: #0056b3;
}

.file-hint {
  margin-top: 16px;
  color: #6c757d;
  font-size: 14px;
}

.qr-generator {
  padding: 20px;
  display: flex;
  gap: 20px;
}

.generator-form {
  flex: 1;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #495057;
}

.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.form-group select:focus,
.form-group textarea:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.generate-btn {
  padding: 10px 20px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.generate-btn:hover:not(:disabled) {
  background: #218838;
}

.generate-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.generated-qr {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.generated-qr img {
  border: 1px solid #dee2e6;
  border-radius: 8px;
}

.qr-actions {
  display: flex;
  gap: 8px;
}

.qr-actions button {
  padding: 6px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s ease;
}

.qr-actions button:hover {
  background: #f8f9fa;
}

.qr-history {
  width: 350px;
  background: #f8f9fa;
  border-left: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.qr-history h3 {
  margin: 0;
  padding: 16px 20px;
  font-size: 16px;
  font-weight: 600;
  color: #212529;
  border-bottom: 1px solid #e9ecef;
}

.empty-history {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #6c757d;
  font-size: 14px;
}

.history-list {
  flex: 1;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.history-item:hover {
  background: #e9ecef;
}

.history-item.selected {
  background: #cce5ff;
  border-left: 3px solid #007bff;
}

.qr-icon {
  font-size: 20px;
  margin-right: 12px;
  flex-shrink: 0;
}

.qr-info {
  flex: 1;
  min-width: 0;
}

.qr-content {
  font-size: 13px;
  font-weight: 500;
  color: #212529;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.qr-meta {
  font-size: 11px;
  color: #6c757d;
}

.qr-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.history-item:hover .qr-actions {
  opacity: 1;
}

.qr-actions button {
  padding: 4px 6px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 3px;
  font-size: 12px;
  transition: background-color 0.2s ease;
}

.qr-actions button:hover {
  background: rgba(0, 0, 0, 0.1);
}

.qr-details {
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  flex-shrink: 0;
}

.qr-details h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #212529;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 6px 0;
  font-size: 13px;
  border-bottom: 1px solid #e9ecef;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item span:first-child {
  color: #6c757d;
  font-weight: 500;
  min-width: 100px;
}

.detail-item span:last-child {
  color: #212529;
  text-align: right;
  word-break: break-word;
  max-width: 60%;
}

/* Темная тема */
@media (prefers-color-scheme: dark) {
  .qr-scanner {
    background: #1a1a1a;
    color: #ffffff;
  }

  .scanner-controls,
  .camera-controls,
  .qr-history,
  .qr-details {
    background: #2d2d2d;
    border-color: #404040;
  }

  .mode-selector button,
  .camera-controls select,
  .form-group select,
  .form-group textarea,
  .qr-actions button {
    background: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
  }

  .mode-selector button:hover,
  .qr-actions button:hover {
    background: #404040;
  }

  .scanner-main {
    background: #1a1a1a;
  }

  .history-item {
    border-bottom-color: #404040;
  }

  .history-item:hover {
    background: #404040;
  }

  .history-item.selected {
    background: #1a4480;
  }

  .qr-content {
    color: #ffffff;
  }

  .qr-details h3 {
    color: #ffffff;
  }

  .detail-item span:last-child {
    color: #ffffff;
  }
}

/* Адаптивность */
@media (max-width: 768px) {
  .scanner-content {
    flex-direction: column;
  }

  .qr-history {
    width: 100%;
    max-height: 300px;
  }

  .qr-generator {
    flex-direction: column;
  }

  .camera-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}
