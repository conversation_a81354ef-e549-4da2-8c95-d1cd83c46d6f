import React, { useState, useEffect, useRef, useCallback } from 'react';
import { IntelligentTextProcessor, TextDocument, TextStatistics } from '../../../core/text-editor/advanced-text-editor';
import './TextEditor.module.css';

interface TextEditorProps {
  documentId?: string;
  className?: string;
  onSave?: (document: TextDocument) => void;
  onClose?: () => void;
}

export const TextEditor: React.FC<TextEditorProps> = ({ 
  documentId, 
  className, 
  onSave, 
  onClose 
}) => {
  const [textProcessor] = useState(() => new IntelligentTextProcessor());
  const [document, setDocument] = useState<TextDocument | null>(null);
  const [content, setContent] = useState('');
  const [title, setTitle] = useState('Новый документ');
  const [isModified, setIsModified] = useState(false);
  const [statistics, setStatistics] = useState<TextStatistics | null>(null);
  const [showStatistics, setShowStatistics] = useState(false);
  const [format, setFormat] = useState<'plain' | 'markdown' | 'html' | 'rich'>('plain');
  const [fontSize, setFontSize] = useState(14);
  const [wordWrap, setWordWrap] = useState(true);
  const [showFind, setShowFind] = useState(false);
  const [findQuery, setFindQuery] = useState('');
  const [replaceQuery, setReplaceQuery] = useState('');

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();

  // Загрузка документа
  useEffect(() => {
    if (documentId) {
      const doc = textProcessor.getDocument(documentId);
      if (doc) {
        setDocument(doc);
        setContent(doc.content);
        setTitle(doc.title);
        setFormat(doc.format);
        setIsModified(false);
      }
    }
  }, [documentId, textProcessor]);

  // Автосохранение
  useEffect(() => {
    if (isModified && document) {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }

      autoSaveTimeoutRef.current = setTimeout(async () => {
        await handleSave();
      }, 2000); // Автосохранение через 2 секунды
    }

    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [isModified, content, title]);

  // Обновление статистики
  useEffect(() => {
    if (content) {
      const stats = textProcessor.getTextStatistics(content);
      setStatistics(stats);
    }
  }, [content, textProcessor]);

  // Обработка изменения содержимого
  const handleContentChange = useCallback((newContent: string) => {
    setContent(newContent);
    setIsModified(true);
  }, []);

  // Обработка изменения заголовка
  const handleTitleChange = useCallback((newTitle: string) => {
    setTitle(newTitle);
    setIsModified(true);
  }, []);

  // Сохранение документа
  const handleSave = async () => {
    try {
      let docId = documentId;
      
      if (!docId) {
        // Создать новый документ
        docId = await textProcessor.createDocument(title, content, format);
      } else {
        // Обновить существующий документ
        await textProcessor.updateDocument(docId, content);
      }

      const savedDoc = textProcessor.getDocument(docId);
      if (savedDoc) {
        setDocument(savedDoc);
        setIsModified(false);
        onSave?.(savedDoc);
      }
    } catch (error) {
      console.error('Failed to save document:', error);
    }
  };

  // Экспорт документа
  const handleExport = async (exportFormat: 'txt' | 'md' | 'html' | 'pdf' | 'docx') => {
    if (!document) return;

    try {
      const blob = await textProcessor.exportDocument(document.id, exportFormat);
      if (blob) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${document.title}.${exportFormat}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Failed to export document:', error);
    }
  };

  // Поиск и замена
  const handleFind = () => {
    if (!textareaRef.current || !findQuery) return;

    const textarea = textareaRef.current;
    const text = textarea.value;
    const index = text.toLowerCase().indexOf(findQuery.toLowerCase());

    if (index !== -1) {
      textarea.focus();
      textarea.setSelectionRange(index, index + findQuery.length);
    }
  };

  const handleReplace = () => {
    if (!textareaRef.current || !findQuery) return;

    const textarea = textareaRef.current;
    const newContent = content.replace(new RegExp(findQuery, 'gi'), replaceQuery);
    handleContentChange(newContent);
  };

  // Вставка форматирования для Markdown
  const insertMarkdown = (before: string, after: string = '') => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);
    
    const newText = before + selectedText + after;
    const newContent = content.substring(0, start) + newText + content.substring(end);
    
    handleContentChange(newContent);
    
    // Восстановить фокус и выделение
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + before.length, start + before.length + selectedText.length);
    }, 0);
  };

  // Горячие клавиши
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 's':
          e.preventDefault();
          handleSave();
          break;
        case 'f':
          e.preventDefault();
          setShowFind(!showFind);
          break;
        case 'b':
          if (format === 'markdown') {
            e.preventDefault();
            insertMarkdown('**', '**');
          }
          break;
        case 'i':
          if (format === 'markdown') {
            e.preventDefault();
            insertMarkdown('*', '*');
          }
          break;
      }
    }
  };

  return (
    <div className={`text-editor ${className || ''}`}>
      {/* Панель инструментов */}
      <div className="text-editor-toolbar">
        <div className="toolbar-left">
          <input
            type="text"
            value={title}
            onChange={(e) => handleTitleChange(e.target.value)}
            className="document-title"
            placeholder="Название документа"
          />
          
          <select
            value={format}
            onChange={(e) => setFormat(e.target.value as any)}
            className="format-select"
          >
            <option value="plain">Обычный текст</option>
            <option value="markdown">Markdown</option>
            <option value="html">HTML</option>
            <option value="rich">Rich Text</option>
          </select>
        </div>

        <div className="toolbar-right">
          <button onClick={() => setShowFind(!showFind)} title="Найти (Ctrl+F)">
            🔍
          </button>
          <button onClick={() => setShowStatistics(!showStatistics)} title="Статистика">
            📊
          </button>
          <button onClick={handleSave} disabled={!isModified} title="Сохранить (Ctrl+S)">
            💾
          </button>
          
          <div className="export-dropdown">
            <button title="Экспорт">📤</button>
            <div className="dropdown-content">
              <button onClick={() => handleExport('txt')}>Текст (.txt)</button>
              <button onClick={() => handleExport('md')}>Markdown (.md)</button>
              <button onClick={() => handleExport('html')}>HTML (.html)</button>
            </div>
          </div>

          {onClose && (
            <button onClick={onClose} title="Закрыть">
              ✕
            </button>
          )}
        </div>
      </div>

      {/* Панель форматирования для Markdown */}
      {format === 'markdown' && (
        <div className="formatting-toolbar">
          <button onClick={() => insertMarkdown('**', '**')} title="Жирный">
            <strong>B</strong>
          </button>
          <button onClick={() => insertMarkdown('*', '*')} title="Курсив">
            <em>I</em>
          </button>
          <button onClick={() => insertMarkdown('# ', '')} title="Заголовок">
            H1
          </button>
          <button onClick={() => insertMarkdown('- ', '')} title="Список">
            • List
          </button>
          <button onClick={() => insertMarkdown('`', '`')} title="Код">
            &lt;/&gt;
          </button>
          <button onClick={() => insertMarkdown('[', '](url)')} title="Ссылка">
            🔗
          </button>
        </div>
      )}

      {/* Панель поиска */}
      {showFind && (
        <div className="find-panel">
          <input
            type="text"
            placeholder="Найти..."
            value={findQuery}
            onChange={(e) => setFindQuery(e.target.value)}
            className="find-input"
          />
          <input
            type="text"
            placeholder="Заменить на..."
            value={replaceQuery}
            onChange={(e) => setReplaceQuery(e.target.value)}
            className="replace-input"
          />
          <button onClick={handleFind}>Найти</button>
          <button onClick={handleReplace}>Заменить</button>
          <button onClick={() => setShowFind(false)}>✕</button>
        </div>
      )}

      {/* Основная область редактирования */}
      <div className="editor-main">
        <textarea
          ref={textareaRef}
          value={content}
          onChange={(e) => handleContentChange(e.target.value)}
          onKeyDown={handleKeyDown}
          className="editor-textarea"
          style={{
            fontSize: `${fontSize}px`,
            whiteSpace: wordWrap ? 'pre-wrap' : 'pre'
          }}
          placeholder="Начните печатать..."
          spellCheck={true}
        />

        {/* Панель статистики */}
        {showStatistics && statistics && (
          <div className="statistics-panel">
            <h3>Статистика документа</h3>
            <div className="stat-item">
              <span>Слов:</span>
              <span>{statistics.wordCount}</span>
            </div>
            <div className="stat-item">
              <span>Символов:</span>
              <span>{statistics.characterCount}</span>
            </div>
            <div className="stat-item">
              <span>Символов без пробелов:</span>
              <span>{statistics.characterCountNoSpaces}</span>
            </div>
            <div className="stat-item">
              <span>Абзацев:</span>
              <span>{statistics.paragraphCount}</span>
            </div>
            <div className="stat-item">
              <span>Предложений:</span>
              <span>{statistics.sentenceCount}</span>
            </div>
            <div className="stat-item">
              <span>Время чтения:</span>
              <span>{statistics.readingTime} мин</span>
            </div>
            <div className="stat-item">
              <span>Читаемость:</span>
              <span>{Math.round(statistics.readabilityScore)}</span>
            </div>
          </div>
        )}
      </div>

      {/* Строка состояния */}
      <div className="status-bar">
        <div className="status-left">
          {isModified && <span className="modified-indicator">● Изменено</span>}
          {statistics && (
            <span>
              Строка {content.substring(0, textareaRef.current?.selectionStart || 0).split('\n').length} | 
              Слов: {statistics.wordCount} | 
              Символов: {statistics.characterCount}
            </span>
          )}
        </div>

        <div className="status-right">
          <label>
            Размер шрифта:
            <input
              type="range"
              min="10"
              max="24"
              value={fontSize}
              onChange={(e) => setFontSize(Number(e.target.value))}
            />
            {fontSize}px
          </label>
          
          <label>
            <input
              type="checkbox"
              checked={wordWrap}
              onChange={(e) => setWordWrap(e.target.checked)}
            />
            Перенос строк
          </label>
        </div>
      </div>
    </div>
  );
};
