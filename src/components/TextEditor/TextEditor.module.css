.text-editor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.text-editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.document-title {
  padding: 6px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  min-width: 200px;
  outline: none;
}

.document-title:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.format-select {
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  outline: none;
}

.toolbar-right button {
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.toolbar-right button:hover {
  background: #f8f9fa;
}

.toolbar-right button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.export-dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  background: white;
  border: 1px solid #ced4da;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 150px;
}

.export-dropdown:hover .dropdown-content {
  display: block;
}

.dropdown-content button {
  display: block;
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
}

.dropdown-content button:hover {
  background: #f8f9fa;
}

.formatting-toolbar {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 16px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.formatting-toolbar button {
  padding: 4px 8px;
  border: 1px solid #ced4da;
  border-radius: 3px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.formatting-toolbar button:hover {
  background: #f8f9fa;
}

.find-panel {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #fff3cd;
  border-bottom: 1px solid #ffeaa7;
  flex-shrink: 0;
}

.find-input,
.replace-input {
  padding: 4px 8px;
  border: 1px solid #ced4da;
  border-radius: 3px;
  font-size: 14px;
  outline: none;
}

.find-input {
  width: 150px;
}

.replace-input {
  width: 120px;
}

.find-panel button {
  padding: 4px 8px;
  border: 1px solid #ced4da;
  border-radius: 3px;
  background: white;
  cursor: pointer;
  font-size: 12px;
}

.editor-main {
  display: flex;
  flex: 1;
  min-height: 0;
}

.editor-textarea {
  flex: 1;
  padding: 20px;
  border: none;
  outline: none;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
  resize: none;
  background: #ffffff;
  color: #212529;
}

.editor-textarea::placeholder {
  color: #6c757d;
  font-style: italic;
}

.statistics-panel {
  width: 250px;
  padding: 16px;
  background: #f8f9fa;
  border-left: 1px solid #e9ecef;
  overflow-y: auto;
  flex-shrink: 0;
}

.statistics-panel h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #212529;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-item span:first-child {
  color: #6c757d;
}

.stat-item span:last-child {
  font-weight: 500;
  color: #212529;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  font-size: 12px;
  color: #6c757d;
  flex-shrink: 0;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.modified-indicator {
  color: #007bff;
  font-weight: 500;
}

.status-right label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  cursor: pointer;
}

.status-right input[type="range"] {
  width: 60px;
}

.status-right input[type="checkbox"] {
  margin: 0;
}

/* Темная тема */
@media (prefers-color-scheme: dark) {
  .text-editor {
    background: #1a1a1a;
    color: #ffffff;
  }

  .text-editor-toolbar,
  .status-bar {
    background: #2d2d2d;
    border-color: #404040;
  }

  .formatting-toolbar {
    background: #1a1a1a;
    border-color: #404040;
  }

  .document-title,
  .format-select,
  .find-input,
  .replace-input {
    background: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
  }

  .toolbar-right button,
  .formatting-toolbar button,
  .find-panel button {
    background: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
  }

  .toolbar-right button:hover,
  .formatting-toolbar button:hover,
  .find-panel button:hover {
    background: #404040;
  }

  .dropdown-content {
    background: #2d2d2d;
    border-color: #404040;
  }

  .dropdown-content button:hover {
    background: #404040;
  }

  .editor-textarea {
    background: #1a1a1a;
    color: #ffffff;
  }

  .editor-textarea::placeholder {
    color: #6c757d;
  }

  .statistics-panel {
    background: #2d2d2d;
    border-color: #404040;
  }

  .statistics-panel h3 {
    color: #ffffff;
  }

  .stat-item {
    border-color: #404040;
  }

  .stat-item span:last-child {
    color: #ffffff;
  }

  .find-panel {
    background: #3d3d00;
    border-color: #666600;
  }
}

/* Адаптивность */
@media (max-width: 768px) {
  .toolbar-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .document-title {
    min-width: 150px;
  }

  .statistics-panel {
    width: 200px;
  }

  .status-right {
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .editor-main {
    flex-direction: column;
  }

  .statistics-panel {
    width: 100%;
    max-height: 200px;
    order: -1;
  }

  .toolbar-right {
    flex-wrap: wrap;
    gap: 4px;
  }

  .status-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
