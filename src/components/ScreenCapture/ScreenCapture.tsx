import React, { useState, useEffect, useRef } from 'react';
import { ScreenCaptureEngine, Screenshot, CaptureOptions, Annotation } from '../../../core/screen-capture/advanced-screenshot-system';
import './ScreenCapture.module.css';

interface ScreenCaptureProps {
  className?: string;
  onScreenshotTaken?: (screenshot: Screenshot) => void;
}

export const ScreenCapture: React.FC<ScreenCaptureProps> = ({ 
  className, 
  onScreenshotTaken 
}) => {
  const [captureEngine] = useState(() => new ScreenCaptureEngine());
  const [screenshots, setScreenshots] = useState<Screenshot[]>([]);
  const [selectedScreenshot, setSelectedScreenshot] = useState<Screenshot | null>(null);
  const [isCapturing, setIsCapturing] = useState(false);
  const [captureMode, setCaptureMode] = useState<'visible' | 'fullpage' | 'selection'>('visible');
  const [showAnnotationTools, setShowAnnotationTools] = useState(false);
  const [annotationTool, setAnnotationTool] = useState<'arrow' | 'rectangle' | 'text' | 'highlight'>('arrow');

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  // Загрузка сохраненных скриншотов
  useEffect(() => {
    loadScreenshots();
  }, []);

  const loadScreenshots = async () => {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith('screenshot_'));
      const loadedScreenshots: Screenshot[] = [];
      
      for (const key of keys) {
        const data = localStorage.getItem(key);
        if (data) {
          try {
            const screenshot = JSON.parse(data);
            loadedScreenshots.push(screenshot);
          } catch (error) {
            console.error('Failed to parse screenshot:', error);
          }
        }
      }
      
      setScreenshots(loadedScreenshots.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      ));
    } catch (error) {
      console.error('Failed to load screenshots:', error);
    }
  };

  // Захват экрана
  const handleCapture = async () => {
    if (isCapturing) return;

    setIsCapturing(true);
    try {
      const options: CaptureOptions = {
        format: 'png',
        quality: 0.9,
        title: `Screenshot ${new Date().toLocaleString()}`,
        category: 'general'
      };

      let screenshot: Screenshot;

      switch (captureMode) {
        case 'visible':
          screenshot = await captureEngine.captureVisibleArea(options);
          break;
        case 'fullpage':
          screenshot = await captureEngine.captureFullPage(options);
          break;
        case 'selection':
          screenshot = await captureEngine.captureVisibleArea(options);
          break;
        default:
          throw new Error('Invalid capture mode');
      }

      setScreenshots(prev => [screenshot, ...prev]);
      setSelectedScreenshot(screenshot);
      onScreenshotTaken?.(screenshot);
    } catch (error) {
      console.error('Failed to capture screenshot:', error);
      alert('Не удалось сделать скриншот. Убедитесь, что вы предоставили разрешение на захват экрана.');
    } finally {
      setIsCapturing(false);
    }
  };

  // Удаление скриншота
  const handleDelete = async (screenshotId: string) => {
    if (confirm('Вы уверены, что хотите удалить этот скриншот?')) {
      try {
        localStorage.removeItem(`screenshot_${screenshotId}`);
        setScreenshots(prev => prev.filter(s => s.id !== screenshotId));
        
        if (selectedScreenshot?.id === screenshotId) {
          setSelectedScreenshot(null);
        }
      } catch (error) {
        console.error('Failed to delete screenshot:', error);
      }
    }
  };

  // Скачивание скриншота
  const handleDownload = async (screenshot: Screenshot) => {
    try {
      const response = await fetch(screenshot.url);
      const blob = await response.blob();
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${screenshot.title}.${screenshot.format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download screenshot:', error);
    }
  };

  // Копирование в буфер обмена
  const handleCopyToClipboard = async (screenshot: Screenshot) => {
    try {
      const response = await fetch(screenshot.url);
      const blob = await response.blob();
      
      await navigator.clipboard.write([
        new ClipboardItem({ [blob.type]: blob })
      ]);
      
      alert('Скриншот скопирован в буфер обмена!');
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      alert('Не удалось скопировать в буфер обмена');
    }
  };

  // Форматирование размера файла
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`screen-capture ${className || ''}`}>
      {/* Панель управления */}
      <div className="capture-controls">
        <div className="capture-modes">
          <label>
            <input
              type="radio"
              value="visible"
              checked={captureMode === 'visible'}
              onChange={(e) => setCaptureMode(e.target.value as any)}
            />
            Видимая область
          </label>
          <label>
            <input
              type="radio"
              value="fullpage"
              checked={captureMode === 'fullpage'}
              onChange={(e) => setCaptureMode(e.target.value as any)}
            />
            Полная страница
          </label>
          <label>
            <input
              type="radio"
              value="selection"
              checked={captureMode === 'selection'}
              onChange={(e) => setCaptureMode(e.target.value as any)}
            />
            Выделенная область
          </label>
        </div>

        <button
          onClick={handleCapture}
          disabled={isCapturing}
          className="capture-button"
        >
          {isCapturing ? '📸 Захват...' : '📸 Сделать скриншот'}
        </button>
      </div>

      <div className="capture-content">
        {/* Список скриншотов */}
        <div className="screenshots-list">
          <h3>Скриншоты ({screenshots.length})</h3>
          
          {screenshots.length === 0 ? (
            <div className="empty-state">
              <p>Нет сохраненных скриншотов</p>
            </div>
          ) : (
            <div className="screenshots-grid">
              {screenshots.map((screenshot) => (
                <div
                  key={screenshot.id}
                  className={`screenshot-item ${selectedScreenshot?.id === screenshot.id ? 'selected' : ''}`}
                  onClick={() => setSelectedScreenshot(screenshot)}
                >
                  <img
                    src={screenshot.url}
                    alt={screenshot.title}
                    className="screenshot-thumbnail"
                  />
                  
                  <div className="screenshot-info">
                    <div className="screenshot-title">{screenshot.title}</div>
                    <div className="screenshot-meta">
                      {screenshot.dimensions.width}×{screenshot.dimensions.height} • {formatFileSize(screenshot.size)}
                    </div>
                    <div className="screenshot-date">
                      {new Date(screenshot.timestamp).toLocaleString()}
                    </div>
                  </div>

                  <div className="screenshot-actions">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDownload(screenshot);
                      }}
                      title="Скачать"
                    >
                      ⬇️
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCopyToClipboard(screenshot);
                      }}
                      title="Копировать"
                    >
                      📋
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(screenshot.id);
                      }}
                      title="Удалить"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Просмотр и редактирование */}
        {selectedScreenshot && (
          <div className="screenshot-viewer">
            <div className="viewer-header">
              <h3>{selectedScreenshot.title}</h3>
              
              <div className="viewer-controls">
                <button
                  onClick={() => setShowAnnotationTools(!showAnnotationTools)}
                  className={showAnnotationTools ? 'active' : ''}
                >
                  ✏️ Аннотации
                </button>
              </div>
            </div>

            {/* Панель инструментов аннотации */}
            {showAnnotationTools && (
              <div className="annotation-tools">
                <div className="tool-group">
                  <label>Инструмент:</label>
                  <select
                    value={annotationTool}
                    onChange={(e) => setAnnotationTool(e.target.value as any)}
                  >
                    <option value="arrow">Стрелка</option>
                    <option value="rectangle">Прямоугольник</option>
                    <option value="text">Текст</option>
                    <option value="highlight">Выделение</option>
                  </select>
                </div>
              </div>
            )}

            {/* Изображение */}
            <div className="image-container">
              <img
                ref={imageRef}
                src={selectedScreenshot.url}
                alt={selectedScreenshot.title}
                className="screenshot-image"
              />
            </div>

            {/* Информация о скриншоте */}
            <div className="screenshot-details">
              <div className="detail-item">
                <span>Размер:</span>
                <span>{selectedScreenshot.dimensions.width}×{selectedScreenshot.dimensions.height}</span>
              </div>
              <div className="detail-item">
                <span>Формат:</span>
                <span>{selectedScreenshot.format.toUpperCase()}</span>
              </div>
              <div className="detail-item">
                <span>Размер файла:</span>
                <span>{formatFileSize(selectedScreenshot.size)}</span>
              </div>
              <div className="detail-item">
                <span>Создан:</span>
                <span>{new Date(selectedScreenshot.timestamp).toLocaleString()}</span>
              </div>
              {selectedScreenshot.metadata.pageUrl && (
                <div className="detail-item">
                  <span>Страница:</span>
                  <span>{selectedScreenshot.metadata.pageTitle}</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
