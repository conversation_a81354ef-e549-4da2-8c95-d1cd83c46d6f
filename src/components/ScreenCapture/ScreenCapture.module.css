.screen-capture {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.capture-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.capture-modes {
  display: flex;
  gap: 20px;
}

.capture-modes label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  cursor: pointer;
  color: #495057;
}

.capture-modes input[type="radio"] {
  margin: 0;
}

.capture-button {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.capture-button:hover:not(:disabled) {
  background: #0056b3;
}

.capture-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.capture-content {
  display: flex;
  flex: 1;
  min-height: 0;
}

.screenshots-list {
  width: 300px;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.screenshots-list h3 {
  margin: 0;
  padding: 16px 20px;
  font-size: 16px;
  font-weight: 600;
  color: #212529;
  border-bottom: 1px solid #e9ecef;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #6c757d;
  font-size: 14px;
}

.screenshots-grid {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.screenshot-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.screenshot-item:hover {
  background: #e9ecef;
}

.screenshot-item.selected {
  background: #cce5ff;
  border-left: 3px solid #007bff;
}

.screenshot-thumbnail {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  flex-shrink: 0;
}

.screenshot-info {
  flex: 1;
  margin-left: 12px;
  min-width: 0;
}

.screenshot-title {
  font-size: 13px;
  font-weight: 500;
  color: #212529;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.screenshot-meta {
  font-size: 11px;
  color: #6c757d;
  margin-bottom: 2px;
}

.screenshot-date {
  font-size: 11px;
  color: #6c757d;
}

.screenshot-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.screenshot-item:hover .screenshot-actions {
  opacity: 1;
}

.screenshot-actions button {
  padding: 4px 6px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 3px;
  font-size: 12px;
  transition: background-color 0.2s ease;
}

.screenshot-actions button:hover {
  background: rgba(0, 0, 0, 0.1);
}

.screenshot-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #ffffff;
}

.viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.viewer-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #212529;
}

.viewer-controls {
  display: flex;
  gap: 8px;
}

.viewer-controls button {
  padding: 6px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s ease;
}

.viewer-controls button:hover {
  background: #f8f9fa;
}

.viewer-controls button.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.annotation-tools {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 20px;
  background: #fff3cd;
  border-bottom: 1px solid #ffeaa7;
  flex-shrink: 0;
}

.tool-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-group label {
  font-size: 13px;
  font-weight: 500;
  color: #495057;
}

.tool-group select {
  padding: 4px 8px;
  border: 1px solid #ced4da;
  border-radius: 3px;
  font-size: 13px;
  background: white;
}

.image-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  overflow: auto;
  background: #f8f9fa;
  position: relative;
}

.screenshot-image {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;
}

.annotation-canvas {
  position: absolute;
  top: 0;
  left: 0;
  cursor: crosshair;
  z-index: 10;
}

.screenshot-details {
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  flex-shrink: 0;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 13px;
}

.detail-item span:first-child {
  color: #6c757d;
  font-weight: 500;
}

.detail-item span:last-child {
  color: #212529;
  text-align: right;
  max-width: 60%;
  word-break: break-word;
}

/* Темная тема */
@media (prefers-color-scheme: dark) {
  .screen-capture {
    background: #1a1a1a;
    color: #ffffff;
  }

  .capture-controls {
    background: #2d2d2d;
    border-bottom-color: #404040;
  }

  .capture-modes label {
    color: #ffffff;
  }

  .screenshots-list {
    background: #2d2d2d;
    border-right-color: #404040;
  }

  .screenshots-list h3 {
    color: #ffffff;
    border-bottom-color: #404040;
  }

  .screenshot-item {
    border-bottom-color: #404040;
  }

  .screenshot-item:hover {
    background: #404040;
  }

  .screenshot-item.selected {
    background: #1a4480;
  }

  .screenshot-title {
    color: #ffffff;
  }

  .screenshot-viewer {
    background: #1a1a1a;
  }

  .viewer-header {
    border-bottom-color: #404040;
  }

  .viewer-header h3 {
    color: #ffffff;
  }

  .viewer-controls button {
    background: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
  }

  .viewer-controls button:hover {
    background: #404040;
  }

  .annotation-tools {
    background: #3d3d00;
    border-bottom-color: #666600;
  }

  .tool-group label {
    color: #ffffff;
  }

  .tool-group select {
    background: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
  }

  .image-container {
    background: #2d2d2d;
  }

  .screenshot-details {
    background: #2d2d2d;
    border-top-color: #404040;
  }

  .detail-item span:last-child {
    color: #ffffff;
  }
}

/* Адаптивность */
@media (max-width: 768px) {
  .capture-content {
    flex-direction: column;
  }

  .screenshots-list {
    width: 100%;
    max-height: 300px;
  }

  .screenshots-grid {
    max-height: 250px;
  }

  .capture-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .capture-modes {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .capture-modes {
    flex-direction: column;
    gap: 8px;
  }

  .screenshot-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .screenshot-thumbnail {
    width: 100%;
    height: 60px;
  }

  .screenshot-info {
    margin-left: 0;
    width: 100%;
  }

  .screenshot-actions {
    opacity: 1;
    align-self: flex-end;
  }
}
