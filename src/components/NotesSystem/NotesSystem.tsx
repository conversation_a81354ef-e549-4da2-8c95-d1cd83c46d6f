import React, { useState, useEffect, useRef } from 'react';
import { IntelligentNotesManager, Note, PageSticker, NotesFilters } from '../../../core/notes-system/advanced-notes-system';
import './NotesSystem.module.css';

interface NotesSystemProps {
  className?: string;
  onNoteCreated?: (note: Note) => void;
}

export const NotesSystem: React.FC<NotesSystemProps> = ({ 
  className, 
  onNoteCreated 
}) => {
  const [notesManager] = useState(() => new IntelligentNotesManager());
  const [notes, setNotes] = useState<Note[]>([]);
  const [selectedNote, setSelectedNote] = useState<Note | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<NotesFilters>({});
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'stickers'>('list');
  const [pageStickers, setPageStickers] = useState<PageSticker[]>([]);

  // Новая заметка
  const [newNote, setNewNote] = useState({
    title: '',
    content: '',
    type: 'text' as const,
    category: 'general',
    color: '#ffeb3b',
    priority: 'normal' as const,
    tags: [] as string[]
  });

  const [newTag, setNewTag] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Загрузка заметок
  useEffect(() => {
    loadNotes();
    loadPageStickers();
  }, []);

  // Обновление при изменении поиска и фильтров
  useEffect(() => {
    filterNotes();
  }, [searchQuery, filters]);

  const loadNotes = async () => {
    const allNotes = notesManager.getAllNotes();
    setNotes(allNotes);
  };

  const loadPageStickers = () => {
    const stickers = notesManager.getPageStickers();
    setPageStickers(stickers);
  };

  const filterNotes = async () => {
    if (searchQuery || Object.keys(filters).length > 0) {
      const filteredNotes = await notesManager.searchNotes(searchQuery, filters);
      setNotes(filteredNotes);
    } else {
      loadNotes();
    }
  };

  // Создание заметки
  const handleCreateNote = async () => {
    if (!newNote.title.trim()) return;

    try {
      const noteId = await notesManager.createNote(newNote);
      const createdNote = notesManager.getNote(noteId);
      
      if (createdNote) {
        setNotes(prev => [createdNote, ...prev]);
        setSelectedNote(createdNote);
        onNoteCreated?.(createdNote);
      }

      // Сброс формы
      setNewNote({
        title: '',
        content: '',
        type: 'text',
        category: 'general',
        color: '#ffeb3b',
        priority: 'normal',
        tags: []
      });
      setShowCreateDialog(false);
    } catch (error) {
      console.error('Failed to create note:', error);
      alert('Ошибка при создании заметки');
    }
  };

  // Обновление заметки
  const handleUpdateNote = async (noteId: string, updates: Partial<Note>) => {
    try {
      await notesManager.updateNote(noteId, updates);
      const updatedNote = notesManager.getNote(noteId);
      
      if (updatedNote) {
        setNotes(prev => prev.map(note => note.id === noteId ? updatedNote : note));
        if (selectedNote?.id === noteId) {
          setSelectedNote(updatedNote);
        }
      }
    } catch (error) {
      console.error('Failed to update note:', error);
    }
  };

  // Удаление заметки
  const handleDeleteNote = async (noteId: string) => {
    if (!confirm('Удалить эту заметку?')) return;

    try {
      await notesManager.deleteNote(noteId);
      setNotes(prev => prev.filter(note => note.id !== noteId));
      
      if (selectedNote?.id === noteId) {
        setSelectedNote(null);
      }
    } catch (error) {
      console.error('Failed to delete note:', error);
    }
  };

  // Создание стикера
  const handleCreateSticker = async (noteId: string) => {
    try {
      const position = { x: 100, y: 100 };
      await notesManager.createPageSticker(noteId, position);
      loadPageStickers();
    } catch (error) {
      console.error('Failed to create sticker:', error);
    }
  };

  // Добавление тега
  const handleAddTag = () => {
    if (newTag.trim() && !newNote.tags.includes(newTag.trim())) {
      setNewNote(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  // Удаление тега
  const handleRemoveTag = (tagToRemove: string) => {
    setNewNote(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // Голосовая запись
  const startVoiceRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      const audioChunks: Blob[] = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunks.push(event.data);
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
        const noteId = await notesManager.createVoiceNote(audioBlob);
        const voiceNote = notesManager.getNote(noteId);
        
        if (voiceNote) {
          setNotes(prev => [voiceNote, ...prev]);
          setSelectedNote(voiceNote);
        }
        
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Failed to start recording:', error);
      alert('Не удалось начать запись. Проверьте разрешения микрофона.');
    }
  };

  const stopVoiceRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  // Веб-клиппинг
  const handleWebClip = async () => {
    try {
      const selection = window.getSelection()?.toString();
      const noteId = await notesManager.clipWebContent(selection);
      const clipNote = notesManager.getNote(noteId);
      
      if (clipNote) {
        setNotes(prev => [clipNote, ...prev]);
        setSelectedNote(clipNote);
      }
    } catch (error) {
      console.error('Failed to clip web content:', error);
    }
  };

  // Экспорт заметок
  const handleExport = async (format: 'json' | 'markdown' | 'html' | 'pdf') => {
    try {
      const blob = await notesManager.exportNotes(format);
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `notes.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export notes:', error);
    }
  };

  // Импорт заметок
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const importedCount = await notesManager.importNotes(file);
      alert(`Импортировано ${importedCount} заметок`);
      loadNotes();
    } catch (error) {
      console.error('Failed to import notes:', error);
      alert('Ошибка при импорте заметок');
    }
  };

  // Получение иконки типа заметки
  const getNoteTypeIcon = (type: string) => {
    switch (type) {
      case 'text': return '📝';
      case 'checklist': return '✅';
      case 'drawing': return '🎨';
      case 'voice': return '🎤';
      case 'link': return '🔗';
      case 'code': return '💻';
      default: return '📄';
    }
  };

  // Получение цвета приоритета
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return '#f44336';
      case 'high': return '#ff9800';
      case 'normal': return '#4caf50';
      case 'low': return '#9e9e9e';
      default: return '#4caf50';
    }
  };

  // Форматирование даты
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString();
  };

  return (
    <div className={`notes-system ${className || ''}`}>
      {/* Панель управления */}
      <div className="notes-header">
        <div className="header-left">
          <h2>Заметки ({notes.length})</h2>
          
          <div className="view-modes">
            <button
              className={viewMode === 'list' ? 'active' : ''}
              onClick={() => setViewMode('list')}
              title="Список"
            >
              📋
            </button>
            <button
              className={viewMode === 'grid' ? 'active' : ''}
              onClick={() => setViewMode('grid')}
              title="Сетка"
            >
              ⊞
            </button>
            <button
              className={viewMode === 'stickers' ? 'active' : ''}
              onClick={() => setViewMode('stickers')}
              title="Стикеры"
            >
              📌
            </button>
          </div>
        </div>

        <div className="header-actions">
          <button onClick={() => setShowCreateDialog(true)} className="create-btn">
            ➕ Создать
          </button>
          
          <button onClick={handleWebClip} className="clip-btn" title="Веб-клиппинг">
            ✂️
          </button>
          
          <button
            onClick={isRecording ? stopVoiceRecording : startVoiceRecording}
            className={`voice-btn ${isRecording ? 'recording' : ''}`}
            title="Голосовая заметка"
          >
            {isRecording ? '⏹️' : '🎤'}
          </button>

          <div className="export-dropdown">
            <button title="Экспорт">📤</button>
            <div className="dropdown-content">
              <button onClick={() => handleExport('json')}>JSON</button>
              <button onClick={() => handleExport('markdown')}>Markdown</button>
              <button onClick={() => handleExport('html')}>HTML</button>
            </div>
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept=".json,.md,.txt"
            onChange={handleImport}
            style={{ display: 'none' }}
          />
          <button onClick={() => fileInputRef.current?.click()} title="Импорт">
            📥
          </button>
        </div>
      </div>

      {/* Поиск и фильтры */}
      <div className="notes-filters">
        <input
          type="text"
          placeholder="Поиск заметок..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="search-input"
        />

        <select
          value={filters.type || ''}
          onChange={(e) => setFilters({ ...filters, type: e.target.value || undefined })}
          className="filter-select"
        >
          <option value="">Все типы</option>
          <option value="text">Текст</option>
          <option value="checklist">Чек-лист</option>
          <option value="voice">Голосовые</option>
          <option value="link">Ссылки</option>
        </select>

        <select
          value={filters.category || ''}
          onChange={(e) => setFilters({ ...filters, category: e.target.value || undefined })}
          className="filter-select"
        >
          <option value="">Все категории</option>
          <option value="general">Общие</option>
          <option value="work">Работа</option>
          <option value="personal">Личное</option>
          <option value="ideas">Идеи</option>
        </select>

        <select
          value={filters.priority || ''}
          onChange={(e) => setFilters({ ...filters, priority: e.target.value || undefined })}
          className="filter-select"
        >
          <option value="">Все приоритеты</option>
          <option value="urgent">Срочно</option>
          <option value="high">Высокий</option>
          <option value="normal">Обычный</option>
          <option value="low">Низкий</option>
        </select>
      </div>

      <div className="notes-content">
        {/* Список заметок */}
        <div className="notes-list">
          {viewMode === 'stickers' ? (
            <div className="stickers-info">
              <p>Стикеры на этой странице: {pageStickers.length}</p>
              {pageStickers.map(sticker => (
                <div key={sticker.id} className="sticker-info">
                  <span>{sticker.content.substring(0, 50)}...</span>
                  <button onClick={() => notesManager.removeSticker(sticker.id)}>
                    🗑️
                  </button>
                </div>
              ))}
            </div>
          ) : (
            <div className={`notes-${viewMode}`}>
              {notes.length === 0 ? (
                <div className="empty-state">
                  <p>Нет заметок</p>
                </div>
              ) : (
                notes.map((note) => (
                  <div
                    key={note.id}
                    className={`note-item ${selectedNote?.id === note.id ? 'selected' : ''}`}
                    onClick={() => setSelectedNote(note)}
                    style={{ borderLeft: `4px solid ${note.color}` }}
                  >
                    <div className="note-header">
                      <div className="note-type">
                        {getNoteTypeIcon(note.type)}
                      </div>
                      <div className="note-title">{note.title}</div>
                      <div 
                        className="note-priority"
                        style={{ color: getPriorityColor(note.priority) }}
                      >
                        ●
                      </div>
                    </div>

                    <div className="note-content-preview">
                      {note.content.substring(0, 100)}
                      {note.content.length > 100 && '...'}
                    </div>

                    <div className="note-meta">
                      <span className="note-category">{note.category}</span>
                      <span className="note-date">{formatDate(note.modified)}</span>
                    </div>

                    {note.tags.length > 0 && (
                      <div className="note-tags">
                        {note.tags.map(tag => (
                          <span key={tag} className="tag">#{tag}</span>
                        ))}
                      </div>
                    )}

                    <div className="note-actions">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCreateSticker(note.id);
                        }}
                        title="Создать стикер"
                      >
                        📌
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteNote(note.id);
                        }}
                        title="Удалить"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>

        {/* Редактор заметки */}
        {selectedNote && (
          <div className="note-editor">
            <div className="editor-header">
              <input
                type="text"
                value={selectedNote.title}
                onChange={(e) => handleUpdateNote(selectedNote.id, { title: e.target.value })}
                className="note-title-input"
              />
              
              <div className="editor-controls">
                <select
                  value={selectedNote.type}
                  onChange={(e) => handleUpdateNote(selectedNote.id, { type: e.target.value as any })}
                >
                  <option value="text">Текст</option>
                  <option value="checklist">Чек-лист</option>
                  <option value="code">Код</option>
                </select>

                <input
                  type="color"
                  value={selectedNote.color}
                  onChange={(e) => handleUpdateNote(selectedNote.id, { color: e.target.value })}
                  title="Цвет"
                />

                <select
                  value={selectedNote.priority}
                  onChange={(e) => handleUpdateNote(selectedNote.id, { priority: e.target.value as any })}
                >
                  <option value="low">Низкий</option>
                  <option value="normal">Обычный</option>
                  <option value="high">Высокий</option>
                  <option value="urgent">Срочно</option>
                </select>
              </div>
            </div>

            <textarea
              value={selectedNote.content}
              onChange={(e) => handleUpdateNote(selectedNote.id, { content: e.target.value })}
              className="note-content-editor"
              placeholder="Содержимое заметки..."
            />

            <div className="note-stats">
              <span>Слов: {selectedNote.metadata.wordCount}</span>
              <span>Символов: {selectedNote.metadata.characterCount}</span>
              <span>Время чтения: {selectedNote.metadata.readingTime} мин</span>
            </div>
          </div>
        )}
      </div>

      {/* Диалог создания заметки */}
      {showCreateDialog && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>Новая заметка</h3>
              <button onClick={() => setShowCreateDialog(false)}>✕</button>
            </div>

            <div className="modal-body">
              <div className="form-group">
                <label>Заголовок:</label>
                <input
                  type="text"
                  value={newNote.title}
                  onChange={(e) => setNewNote({ ...newNote, title: e.target.value })}
                  placeholder="Заголовок заметки"
                  autoFocus
                />
              </div>

              <div className="form-group">
                <label>Содержимое:</label>
                <textarea
                  value={newNote.content}
                  onChange={(e) => setNewNote({ ...newNote, content: e.target.value })}
                  placeholder="Содержимое заметки"
                  rows={5}
                />
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>Тип:</label>
                  <select
                    value={newNote.type}
                    onChange={(e) => setNewNote({ ...newNote, type: e.target.value as any })}
                  >
                    <option value="text">Текст</option>
                    <option value="checklist">Чек-лист</option>
                    <option value="code">Код</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>Категория:</label>
                  <select
                    value={newNote.category}
                    onChange={(e) => setNewNote({ ...newNote, category: e.target.value })}
                  >
                    <option value="general">Общие</option>
                    <option value="work">Работа</option>
                    <option value="personal">Личное</option>
                    <option value="ideas">Идеи</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>Цвет:</label>
                  <input
                    type="color"
                    value={newNote.color}
                    onChange={(e) => setNewNote({ ...newNote, color: e.target.value })}
                  />
                </div>

                <div className="form-group">
                  <label>Приоритет:</label>
                  <select
                    value={newNote.priority}
                    onChange={(e) => setNewNote({ ...newNote, priority: e.target.value as any })}
                  >
                    <option value="low">Низкий</option>
                    <option value="normal">Обычный</option>
                    <option value="high">Высокий</option>
                    <option value="urgent">Срочно</option>
                  </select>
                </div>
              </div>

              <div className="form-group">
                <label>Теги:</label>
                <div className="tags-input">
                  <input
                    type="text"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="Добавить тег"
                    onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                  />
                  <button onClick={handleAddTag}>+</button>
                </div>
                
                <div className="tags-list">
                  {newNote.tags.map(tag => (
                    <span key={tag} className="tag">
                      #{tag}
                      <button onClick={() => handleRemoveTag(tag)}>×</button>
                    </span>
                  ))}
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button onClick={() => setShowCreateDialog(false)}>Отмена</button>
              <button onClick={handleCreateNote} disabled={!newNote.title.trim()}>
                Создать
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
